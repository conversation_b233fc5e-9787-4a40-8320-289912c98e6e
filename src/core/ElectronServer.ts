import { ipc<PERSON><PERSON>, BrowserWindow, app, dialog, shell } from 'electron'
import { EventEmitter } from 'events'
import fs from 'fs/promises'
import os from 'os'
import path from 'path'
import {
  MainProcessAPI,
  MainProcessEvents,
  RendererProcessEvents,
  IPCRequest,
  IPCResponse,
  IPCEvent,
  SystemInfo,
  AppSettings,
  FileSelectOptions,
  DirectorySelectOptions,
  HttpRequestOptions,
  HttpResponse
} from '../method.types'

/**
 * Electron 主进程服务器
 * 负责处理来自渲染进程的IPC调用和事件管理
 */
export class ElectronServer extends EventEmitter {
  private apiHandlers: Partial<MainProcessAPI> = {}
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map()
  private settings: AppSettings = {
    theme: 'auto',
    language: 'zh-CN',
    autoStart: false,
    notifications: true
  }

  constructor() {
    super()
    this.setupIPC()
    this.registerDefaultHandlers()
  }

  /**
   * 设置IPC通信
   */
  private setupIPC(): void {
    // 处理API调用
    ipcMain.handle('electron-api-invoke', async (event, request: IPCRequest) => {
      const response: IPCResponse = {
        id: request.id,
        success: false,
        timestamp: Date.now()
      }

      try {
        const handler = this.apiHandlers[request.method]
        if (!handler) {
          throw new Error(`No handler found for method: ${request.method}`)
        }

        const result = await handler.apply(this, Array.isArray(request.args) ? request.args : [request.args])
        response.success = true
        response.data = result
      } catch (error: any) {
        response.error = {
          message: error.message,
          code: error.code,
          stack: error.stack
        }
      }

      return response
    })

    // 处理渲染进程事件
    ipcMain.on('electron-event-emit', (event, eventData: IPCEvent) => {
      this.emit(`renderer:${eventData.event}`, eventData.data)
    })
  }

  /**
   * 注册默认的API处理器
   */
  private registerDefaultHandlers(): void {
    this.registerAPI({
      // 系统信息相关
      getSystemInfo: async (): Promise<SystemInfo> => {
        return {
          platform: os.platform(),
          arch: os.arch(),
          version: os.release(),
          totalMemory: os.totalmem(),
          freeMemory: os.freemem(),
          cpuCount: os.cpus().length
        }
      },

      getAppVersion: async (): Promise<string> => {
        return app.getVersion()
      },

      // 窗口管理
      minimizeWindow: async (): Promise<void> => {
        const focusedWindow = BrowserWindow.getFocusedWindow()
        if (focusedWindow) {
          focusedWindow.minimize()
        }
      },

      maximizeWindow: async (): Promise<void> => {
        const focusedWindow = BrowserWindow.getFocusedWindow()
        if (focusedWindow) {
          if (focusedWindow.isMaximized()) {
            focusedWindow.unmaximize()
          } else {
            focusedWindow.maximize()
          }
        }
      },

      closeWindow: async (): Promise<void> => {
        const focusedWindow = BrowserWindow.getFocusedWindow()
        if (focusedWindow) {
          focusedWindow.close()
        }
      },

      // 文件操作
      selectFile: async (options?: FileSelectOptions): Promise<string | null> => {
        const result = await dialog.showOpenDialog({
          title: options?.title,
          defaultPath: options?.defaultPath,
          filters: options?.filters,
          properties: options?.properties || ['openFile']
        })

        return result.canceled ? null : result.filePaths[0]
      },

      selectDirectory: async (options?: DirectorySelectOptions): Promise<string | null> => {
        const result = await dialog.showOpenDialog({
          title: options?.title,
          defaultPath: options?.defaultPath,
          properties: options?.properties || ['openDirectory']
        })

        return result.canceled ? null : result.filePaths[0]
      },

      readFile: async (filePath: string): Promise<string> => {
        return await fs.readFile(filePath, 'utf-8')
      },

      writeFile: async (filePath: string, content: string): Promise<void> => {
        await fs.writeFile(filePath, content, 'utf-8')
      },

      // 应用设置
      getSettings: async (): Promise<AppSettings> => {
        return { ...this.settings }
      },

      updateSettings: async (newSettings: Partial<AppSettings>): Promise<void> => {
        this.settings = { ...this.settings, ...newSettings }
        // 这里可以添加持久化逻辑
        this.broadcast('settings-updated', this.settings)
      },

      // 网络请求
      httpRequest: async (options: HttpRequestOptions): Promise<HttpResponse> => {
        const { default: fetch } = await import('node-fetch')
        
        const response = await fetch(options.url, {
          method: options.method || 'GET',
          headers: options.headers,
          body: options.body ? JSON.stringify(options.body) : undefined,
          timeout: options.timeout || 10000
        })

        const data = await response.text()
        
        return {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          data: this.tryParseJSON(data)
        }
      }
    })
  }

  /**
   * 注册API处理器
   */
  public registerAPI(handlers: Partial<MainProcessAPI>): void {
    Object.assign(this.apiHandlers, handlers)
  }

  /**
   * 向所有窗口广播事件
   */
  public broadcast<K extends keyof MainProcessEvents>(
    event: K,
    data: MainProcessEvents[K]
  ): void {
    const eventData: IPCEvent = {
      event: event as string,
      data,
      timestamp: Date.now()
    }

    BrowserWindow.getAllWindows().forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send('electron-event-broadcast', eventData)
      }
    })
  }

  /**
   * 向特定窗口发送事件
   */
  public emit<K extends keyof MainProcessEvents>(
    window: BrowserWindow,
    event: K,
    data: MainProcessEvents[K]
  ): void {
    if (!window.isDestroyed()) {
      const eventData: IPCEvent = {
        event: event as string,
        data,
        timestamp: Date.now()
      }
      window.webContents.send('electron-event-broadcast', eventData)
    }
  }

  /**
   * 监听渲染进程事件
   */
  public onRendererEvent<K extends keyof RendererProcessEvents>(
    event: K,
    listener: (data: RendererProcessEvents[K]) => void
  ): void {
    this.on(`renderer:${event}`, listener)
  }

  /**
   * 尝试解析JSON
   */
  private tryParseJSON(text: string): any {
    try {
      return JSON.parse(text)
    } catch {
      return text
    }
  }
}

// 导出单例实例
export const electronServer = new ElectronServer()
